# API 测试示例集合

本文档提供了当贝AI Provider API的详细测试示例，帮助开发者快速验证API功能。

## 环境准备

确保服务器已启动：
```bash
npm run server
# 或
node start-server.js
```

服务器默认运行在 `http://localhost:3000`

## 1. 基础功能测试

### 健康检查
```bash
# 基础健康检查
curl -X GET http://localhost:3000/health

# 预期响应
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": 1640995200000,
    "uptime": 3600.5,
    "memory": {...},
    "version": "1.0.0"
  }
}
```

### API信息获取
```bash
# 获取API详细信息
curl -X GET http://localhost:3000/api/info

# 查看使用统计
curl -X GET http://localhost:3000/stats
```

## 2. 模型接口测试

### 获取模型列表
```bash
# 获取所有模型
curl -X GET http://localhost:3000/api/models

# 获取推荐模型
curl -X GET http://localhost:3000/api/models/recommended

# 获取特定模型信息
curl -X GET http://localhost:3000/api/models/deepseek
```

### 重新加载模型数据
```bash
curl -X POST http://localhost:3000/api/models/reload \
  -H "Content-Type: application/json"
```

## 3. 聊天接口测试

### 基础聊天（非流式）
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "你好，请介绍一下你自己"}
    ],
    "model": "deepseek",
    "stream": false
  }'
```

### 多轮对话
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "你好"},
      {"role": "assistant", "content": "你好！我是当贝AI助手"},
      {"role": "user", "content": "请帮我写一首诗"}
    ],
    "model": "deepseek",
    "stream": false,
    "conversation_id": "conv_123456"
  }'
```

### 流式聊天
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "请详细解释什么是人工智能"}
    ],
    "model": "deepseek",
    "stream": true
  }' \
  --no-buffer
```

### 带选项的聊天
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {"role": "user", "content": "帮我分析一下当前的AI发展趋势"}
    ],
    "model": "deepseek",
    "stream": false,
    "options": {
      "deep_thinking": true,
      "online_search": false
    }
  }'
```

## 4. 文本生成接口测试

### 创意写作
```bash
curl -X POST http://localhost:3000/api/text/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "请写一首关于春天的现代诗",
    "model": "deepseek",
    "task_type": "creative",
    "stream": false,
    "options": {
      "style": "现代诗",
      "format": "markdown",
      "language": "zh"
    }
  }'
```

### 代码生成
```bash
curl -X POST http://localhost:3000/api/text/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "请用Python写一个快速排序算法",
    "model": "deepseek",
    "task_type": "code",
    "stream": false,
    "max_tokens": 1000,
    "options": {
      "language": "python",
      "format": "markdown"
    }
  }'
```

### 文档生成
```bash
curl -X POST http://localhost:3000/api/text/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "为一个在线购物网站写一份用户使用手册",
    "model": "deepseek",
    "task_type": "document",
    "stream": false,
    "options": {
      "format": "markdown",
      "style": "正式"
    }
  }'
```

### 流式文本生成
```bash
curl -X POST http://localhost:3000/api/text/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "请详细介绍机器学习的基本概念和应用",
    "model": "deepseek",
    "task_type": "qa",
    "stream": true,
    "max_tokens": 2000
  }' \
  --no-buffer
```

## 5. OpenAI兼容接口测试

### 获取模型列表（OpenAI格式）
```bash
curl -X GET http://localhost:3000/v1/models
```

### 聊天对话（OpenAI格式）
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek",
    "messages": [
      {"role": "user", "content": "Hello, how are you today?"}
    ],
    "stream": false,
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

### OpenAI流式聊天
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek",
    "messages": [
      {"role": "user", "content": "Tell me a story about AI"}
    ],
    "stream": true,
    "temperature": 0.8
  }' \
  --no-buffer
```

## 6. 错误处理测试

### 无效模型测试
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "test"}],
    "model": "invalid-model",
    "stream": false
  }'
```

### 缺少必需参数
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek",
    "stream": false
  }'
```

### 无效请求格式
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"invalid": "json"'
```

## 7. 性能测试

### 并发请求测试
```bash
# 使用 Apache Bench 进行并发测试
ab -n 100 -c 10 -T 'application/json' -p chat_request.json http://localhost:3000/api/chat

# chat_request.json 内容：
{
  "messages": [{"role": "user", "content": "Hello"}],
  "model": "deepseek",
  "stream": false
}
```

### 大请求测试
```bash
# 测试大文本输入
curl -X POST http://localhost:3000/api/text/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "请基于以下长文本进行总结...[很长的文本内容]",
    "model": "deepseek",
    "task_type": "summary",
    "max_tokens": 500
  }'
```

## 8. 自动化测试脚本

### Node.js测试脚本示例
```javascript
// test-api-complete.js
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testHealthCheck() {
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ 健康检查通过:', response.data.data.status);
  } catch (error) {
    console.error('❌ 健康检查失败:', error.message);
  }
}

async function testChatAPI() {
  try {
    const response = await axios.post(`${BASE_URL}/api/chat`, {
      messages: [{ role: 'user', content: '你好' }],
      model: 'deepseek',
      stream: false
    });
    console.log('✅ 聊天API测试通过');
  } catch (error) {
    console.error('❌ 聊天API测试失败:', error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  await testHealthCheck();
  await testChatAPI();
  // 添加更多测试...
}

runAllTests();
```

## 9. 测试检查清单

- [ ] 健康检查端点正常响应
- [ ] 模型列表获取成功
- [ ] 聊天接口非流式响应正常
- [ ] 聊天接口流式响应正常
- [ ] 文本生成接口正常工作
- [ ] OpenAI兼容接口正常
- [ ] 错误处理正确返回错误信息
- [ ] 性能在可接受范围内
- [ ] 并发请求处理正常
- [ ] 大请求处理正常

## 10. 故障排除

### 常见问题及解决方案

1. **连接被拒绝**
   - 检查服务器是否启动
   - 确认端口号正确（默认3000）

2. **请求超时**
   - 检查网络连接
   - 增加超时时间设置

3. **模型不可用**
   - 检查models.json文件
   - 重新加载模型数据

4. **流式响应中断**
   - 检查网络稳定性
   - 使用--no-buffer参数

更多详细信息请参考 [HTTP API 完整文档](./HTTP_API_README.md)。
