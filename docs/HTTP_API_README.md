# 当贝AI Provider HTTP API 服务器

## 概述

当贝AI Provider HTTP API 服务器提供了标准的 RESTful API 接口，兼容 OpenAI API 格式，让您可以通过 HTTP 请求轻松访问当贝AI的强大功能。

## 功能特性

- 🤖 **模型列表接口** - 获取所有支持的AI模型信息
- 💬 **聊天对话接口** - 支持流式和非流式聊天响应
- ✍️ **文本生成接口** - 专业的文本生成功能，支持多种生成任务
- 🔄 **OpenAI API 兼容** - 兼容 OpenAI ChatGPT API 格式
- 📊 **实时监控** - 健康检查、使用统计和性能监控
- 🛡️ **安全可靠** - 完整的错误处理和请求验证
- 🚀 **高性能** - 支持并发请求和连接池
- 📝 **详细日志** - 完整的请求响应日志记录

## 快速开始

### 1. 启动服务器

```bash
# 使用默认配置启动（端口3000）
npm run server

# 指定端口启动
npm run server -- --port 8080

# 启用调试模式
npm run server -- --debug

# 指定主机和端口
npm run server -- --host 0.0.0.0 --port 3000
```

### 2. 验证服务器状态

```bash
# 健康检查
curl http://localhost:3000/health

# 获取API信息
curl http://localhost:3000/api/info
```

## API 接口文档

### 基础信息

- **基础URL**: `http://localhost:3000`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 1. 健康检查

**请求**
```http
GET /health
```

**响应**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": **********000,
    "uptime": 3600.5,
    "memory": {
      "rss": 50331648,
      "heapTotal": 20971520,
      "heapUsed": 15728640
    },
    "version": "1.0.0"
  }
}
```

### 2. 模型列表接口

#### 获取所有模型

**请求**
```http
GET /api/models
```

**响应**
```json
{
  "success": true,
  "data": {
    "defaultModel": "deepseek",
    "models": [
      {
        "id": "deepseek",
        "name": "DeepSeek-R1最新版",
        "description": "专注逻辑推理与深度分析，擅长解决复杂问题，提供精准决策支持",
        "options": [
          {
            "name": "深度思考",
            "value": "deep",
            "enabled": true,
            "selected": true
          },
          {
            "name": "联网搜索",
            "value": "online",
            "enabled": true,
            "selected": false
          }
        ],
        "recommended": true,
        "pinned": true,
        "icon": "https://example.com/icon.png",
        "banner": "https://example.com/banner.png",
        "badge": "HOT"
      }
    ],
    "total": 15
  },
  "requestId": "req_1234567890_abc123",
  "timestamp": **********000
}
```

#### 获取特定模型信息

**请求**
```http
GET /api/models/{modelId}
```

**响应**
```json
{
  "success": true,
  "data": {
    "id": "deepseek",
    "name": "DeepSeek-R1最新版",
    "description": "专注逻辑推理与深度分析",
    "options": [...],
    "recommended": true,
    "pinned": true
  },
  "requestId": "req_1234567890_abc123",
  "timestamp": **********000
}
```

#### 获取推荐模型

**请求**
```http
GET /api/models/recommended
```

### 3. 聊天对话接口

#### 发送聊天消息（非流式）

**请求**
```http
POST /api/chat
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己"
    }
  ],
  "model": "deepseek",
  "stream": false,
  "conversation_id": "conv_123456",
  "options": {
    "deep_thinking": true,
    "online_search": false
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "message": {
      "role": "assistant",
      "content": "你好！我是当贝AI助手，基于先进的人工智能技术...",
      "id": "msg_123456",
      "timestamp": **********000
    },
    "conversation_id": "conv_123456",
    "message_id": "msg_123456",
    "parent_message_id": "msg_123455",
    "request_id": "req_123456",
    "model": "deepseek",
    "finish_reason": "stop",
    "usage": {
      "prompt_tokens": 20,
      "completion_tokens": 50,
      "total_tokens": 70
    }
  },
  "requestId": "chat_1234567890_abc123",
  "timestamp": **********000
}
```

#### 发送聊天消息（流式）

**请求**
```http
POST /api/chat
Content-Type: application/json

{
  "messages": [
    {
      "role": "user",
      "content": "请写一首关于春天的诗"
    }
  ],
  "model": "deepseek",
  "stream": true
}
```

**响应（Server-Sent Events）**
```
Content-Type: text/event-stream

data: {"id":"msg_123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"msg_123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"content":"春"},"finish_reason":null}]}

data: {"id":"msg_123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"content":"风"},"finish_reason":null}]}

data: {"id":"msg_123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

### 4. 文本生成接口

#### 生成文本（非流式）

**请求**
```http
POST /api/text/generate
Content-Type: application/json

{
  "prompt": "请写一首关于春天的诗",
  "model": "deepseek",
  "stream": false,
  "task_type": "creative",
  "max_tokens": 1000,
  "temperature": 0.7,
  "options": {
    "style": "现代诗",
    "format": "markdown",
    "language": "zh",
    "deep_thinking": true,
    "online_search": false
  }
}
```

**响应**
```json
{
  "success": true,
  "data": {
    "text": "春风轻抚大地的脸庞\n万物复苏在阳光下绽放\n嫩绿的叶子诉说着希望\n花朵用色彩编织梦想\n\n鸟儿归来唱着欢快的歌\n溪水潺潺流淌着生命的节拍\n蝴蝶翩翩起舞在花间\n春天，是大自然最美的诗篇",
    "model": "deepseek",
    "task_type": "creative",
    "finish_reason": "stop",
    "generation_id": "gen_1234567890",
    "request_id": "req_1234567890",
    "usage": {
      "prompt_tokens": 25,
      "completion_tokens": 120,
      "total_tokens": 145
    },
    "metadata": {
      "generation_time": 2500,
      "parameters": {
        "temperature": 0.7,
        "max_tokens": 1000
      }
    }
  },
  "requestId": "gen_1234567890_abc123",
  "timestamp": **********000
}
```

#### 生成文本（流式）

**请求**
```http
POST /api/text/generate
Content-Type: application/json

{
  "prompt": "请解释什么是人工智能",
  "model": "deepseek",
  "stream": true,
  "task_type": "qa",
  "max_tokens": 500
}
```

**响应（Server-Sent Events）**
```
Content-Type: text/event-stream

data: {"id":"gen_123","object":"text.generation.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"text":"人工智能"},"finish_reason":null}],"task_type":"qa"}

data: {"id":"gen_123","object":"text.generation.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"text":"（AI）"},"finish_reason":null}],"task_type":"qa"}

data: {"id":"gen_123","object":"text.generation.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"text":"是一门"},"finish_reason":null}],"task_type":"qa"}

data: {"id":"gen_123","object":"text.generation.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{},"finish_reason":"stop"}],"task_type":"qa"}

data: [DONE]
```

#### 获取文本生成模型列表

**请求**
```http
GET /api/text/models
```

**响应**
```json
{
  "success": true,
  "data": {
    "defaultModel": "deepseek",
    "models": [
      {
        "id": "deepseek",
        "name": "DeepSeek-R1最新版",
        "description": "专注逻辑推理与深度分析，擅长解决复杂问题",
        "options": [
          {
            "name": "深度思考",
            "value": "deep",
            "enabled": true,
            "selected": true
          }
        ],
        "recommended": true,
        "pinned": true,
        "supported_tasks": ["code", "document", "qa", "general"],
        "max_input_length": 8000,
        "max_output_length": 4000,
        "supports_streaming": true
      }
    ],
    "total": 15,
    "recommendations": {
      "creative": ["doubao-1_6-thinking", "glm-4-5"],
      "code": ["deepseek", "deepseek-v3"],
      "document": ["deepseek", "doubao-1_6-thinking"],
      "summary": ["doubao-1_6-thinking", "qwen-plus"],
      "translation": ["glm-4-5", "glm-4-plus"],
      "rewrite": ["glm-4-5", "doubao-1_6-thinking"],
      "qa": ["deepseek", "qwen-plus"],
      "general": ["deepseek", "doubao-1_6-thinking"]
    }
  },
  "requestId": "models_1234567890_abc123",
  "timestamp": **********000
}
```

#### 文本生成任务类型

支持的任务类型及其说明：

- `creative` - 创意写作：诗歌、小说、故事等创意内容
- `code` - 代码生成：编程代码、脚本、算法实现
- `document` - 文档生成：技术文档、报告、说明书
- `summary` - 摘要生成：文章摘要、内容总结
- `translation` - 翻译：多语言翻译服务
- `rewrite` - 改写：内容改写、润色、优化
- `qa` - 问答：知识问答、解释说明
- `general` - 通用生成：其他类型的文本生成

#### 文本生成选项

可选的生成选项：

- `style` - 写作风格：如"正式"、"幽默"、"学术"等
- `format` - 输出格式：如"markdown"、"html"、"plain"等
- `language` - 输出语言：如"zh"、"en"、"ja"等
- `deep_thinking` - 启用深度思考模式
- `online_search` - 启用联网搜索功能

### 5. 系统接口

#### API信息

**请求**
```http
GET /api/info
```

**响应**
```json
{
  "success": true,
  "data": {
    "name": "当贝AI Provider API",
    "version": "1.0.0",
    "description": "基于当贝AI的标准HTTP API接口",
    "endpoints": {
      "models": {
        "GET /api/models": "获取所有支持的模型列表",
        "GET /api/models/recommended": "获取推荐模型列表",
        "GET /api/models/:modelId": "获取特定模型信息",
        "POST /api/models/reload": "重新加载模型数据"
      },
      "chat": {
        "POST /api/chat": "聊天对话接口（支持流式和非流式）"
      },
      "text": {
        "POST /api/text/generate": "文本生成接口（支持流式和非流式）",
        "GET /api/text/models": "获取支持文本生成的模型列表"
      },
      "system": {
        "GET /api/health": "健康检查",
        "GET /api/info": "API信息",
        "GET /stats": "使用统计"
      }
    },
    "timestamp": **********000
  },
  "requestId": "info_1234567890_abc123",
  "timestamp": **********000
}
```

#### 使用统计

**请求**
```http
GET /stats
```

**响应**
```json
{
  "success": true,
  "data": {
    "usage": {
      "totalRequests": 1250,
      "successfulRequests": 1200,
      "failedRequests": 50,
      "averageResponseTime": 850,
      "requestsPerMinute": 25,
      "endpoints": {
        "/api/chat": {
          "count": 800,
          "averageTime": 1200,
          "errors": 20
        },
        "/api/models": {
          "count": 300,
          "averageTime": 150,
          "errors": 5
        },
        "/api/text/generate": {
          "count": 150,
          "averageTime": 2000,
          "errors": 25
        }
      }
    },
    "system": {
      "uptime": 86400.5,
      "memory": {
        "rss": 67108864,
        "heapTotal": 33554432,
        "heapUsed": 25165824,
        "external": 2097152,
        "arrayBuffers": 1048576
      },
      "cpu": {
        "user": 12345678,
        "system": 2345678
      },
      "version": "v18.17.0",
      "platform": "linux"
    },
    "timestamp": **********000
  },
  "requestId": "stats_1234567890_abc123",
  "timestamp": **********000
}
```

#### 重新加载模型数据

**请求**
```http
POST /api/models/reload
Content-Type: application/json
```

**响应**
```json
{
  "success": true,
  "data": {
    "message": "模型数据重新加载成功",
    "status": {
      "modelsCount": 15,
      "lastUpdated": **********000,
      "defaultModel": "deepseek",
      "supportedFeatures": ["chat", "text_generation", "streaming"]
    }
  },
  "requestId": "reload_1234567890_abc123",
  "timestamp": **********000
}
```

### 6. OpenAI 兼容接口

当贝AI Provider 提供完全兼容 OpenAI API 的接口，方便现有应用快速迁移。

#### 获取模型列表（OpenAI格式）

**请求**
```http
GET /v1/models
```

**响应**
```json
{
  "object": "list",
  "data": [
    {
      "id": "deepseek",
      "object": "model",
      "created": **********,
      "owned_by": "dangbei",
      "permission": [
        {
          "id": "modelperm-123",
          "object": "model_permission",
          "created": **********,
          "allow_create_engine": false,
          "allow_sampling": true,
          "allow_logprobs": false,
          "allow_search_indices": false,
          "allow_view": true,
          "allow_fine_tuning": false,
          "organization": "*",
          "group": null,
          "is_blocking": false
        }
      ],
      "root": "deepseek",
      "parent": null
    }
  ]
}
```

#### 聊天对话（OpenAI格式）

**请求**
```http
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "deepseek",
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己"
    }
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 1000
}
```

**响应（非流式）**
```json
{
  "id": "chatcmpl-123456",
  "object": "chat.completion",
  "created": **********,
  "model": "deepseek",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "你好！我是当贝AI助手，基于先进的人工智能技术..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 20,
    "completion_tokens": 50,
    "total_tokens": 70
  }
}
```

**响应（流式）**
```
Content-Type: text/event-stream

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{"content":"你好"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"deepseek","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 错误处理

所有错误响应都遵循统一格式：

### 标准API错误格式

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息（可选）"
  },
  "requestId": "req_1234567890_abc123",
  "timestamp": **********000
}
```

### OpenAI兼容错误格式

```json
{
  "error": {
    "message": "错误描述",
    "type": "invalid_request_error",
    "param": "model",
    "code": "model_not_found"
  }
}
```

### 常见错误代码

#### 客户端错误 (4xx)

- `INVALID_REQUEST` - 请求参数无效或格式错误
- `UNSUPPORTED_MODEL` - 不支持的模型ID
- `MODEL_NOT_FOUND` - 指定的模型不存在
- `ROUTE_NOT_FOUND` - 请求的API端点不存在
- `INVALID_MESSAGE_FORMAT` - 消息格式不正确
- `MISSING_REQUIRED_FIELD` - 缺少必需的请求字段
- `INVALID_STREAM_PARAMETER` - 流式参数设置错误
- `REQUEST_TOO_LARGE` - 请求体过大（超过10MB限制）

#### 服务器错误 (5xx)

- `CHAT_ERROR` - 聊天请求处理失败
- `GENERATION_ERROR` - 文本生成请求处理失败
- `TIMEOUT_ERROR` - 请求处理超时
- `MODELS_RELOAD_ERROR` - 模型数据重新加载失败
- `INTERNAL_SERVER_ERROR` - 服务器内部错误
- `SERVICE_UNAVAILABLE` - 服务暂时不可用

#### 错误响应示例

**400 Bad Request - 参数无效**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": "model字段是必需的"
  },
  "requestId": "req_1234567890_abc123",
  "timestamp": **********000
}
```

**404 Not Found - 模型不存在**
```json
{
  "success": false,
  "error": {
    "code": "MODEL_NOT_FOUND",
    "message": "指定的模型不存在",
    "details": "模型ID 'invalid-model' 不在支持的模型列表中"
  },
  "requestId": "req_1234567890_abc123",
  "timestamp": **********000
}
```

**500 Internal Server Error - 服务器错误**
```json
{
  "success": false,
  "error": {
    "code": "CHAT_ERROR",
    "message": "聊天请求处理失败",
    "details": "与AI服务的连接超时"
  },
  "requestId": "req_1234567890_abc123",
  "timestamp": **********000
}
```

## 性能和限制

### 请求限制

- **请求体大小限制**: 最大 10MB
- **请求超时时间**: 30秒（可配置）
- **并发连接数**: 无硬性限制，受服务器资源约束
- **流式响应**: 支持 Server-Sent Events (SSE)
- **缓存策略**: 模型列表缓存5分钟

### 性能指标

- **平均响应时间**:
  - 模型列表: ~150ms
  - 聊天对话: ~1200ms
  - 文本生成: ~2000ms
- **吞吐量**: 约25请求/分钟（取决于模型复杂度）
- **内存使用**: 典型运行时约64MB

### 监控和日志

- **慢请求警告**: 响应时间超过1秒的请求会被记录
- **错误率监控**: 自动统计各端点的成功率
- **系统资源监控**: CPU、内存使用情况实时监控
- **请求日志**: 包含请求ID、响应时间、状态码等详细信息

## 环境变量配置

```bash
# 服务器配置
PORT=3000                    # 服务器端口
HOST=0.0.0.0                # 服务器主机地址
NODE_ENV=production          # 运行环境 (development/production)

# 性能配置
TIMEOUT=30000               # 请求超时时间（毫秒）
SLOW_THRESHOLD=1000         # 慢请求阈值（毫秒）
SIZE_THRESHOLD=1048576      # 大请求阈值（字节，1MB）

# CORS配置
CORS_ORIGIN=*               # 允许的源地址
CORS_CREDENTIALS=false      # 是否允许携带凭证

# 调试配置
DEBUG=false                 # 是否启用调试模式
LOG_LEVEL=info             # 日志级别 (error/warn/info/debug)

# 缓存配置
CACHE_TTL=300              # 缓存生存时间（秒）
ENABLE_CACHE=true          # 是否启用缓存

# 安全配置
RATE_LIMIT_ENABLED=false   # 是否启用速率限制
RATE_LIMIT_MAX=100         # 每分钟最大请求数
```

## 性能优化

### 1. 连接池配置

服务器自动管理HTTP连接池，支持并发请求处理。

### 2. 缓存策略

- 模型列表接口缓存5分钟
- 静态资源启用浏览器缓存

### 3. 监控指标

- 请求响应时间
- 错误率统计
- 内存使用情况
- 慢请求警告

## 开发和调试

### 启用调试模式

```bash
npm run server -- --debug
```

调试模式下会输出详细的请求日志，包括：
- 请求和响应详情
- 性能监控信息
- 错误堆栈跟踪

### 测试API

#### 使用内置测试工具

访问 `http://localhost:3000/api-tester` 使用可视化API测试工具。

#### 使用测试脚本

```bash
# 运行完整的API测试套件
node test-http-api.js

# 测试特定功能
node test-models.js          # 测试模型接口
node test-text-generation.js # 测试文本生成
node test-openai-compatibility.js # 测试OpenAI兼容性
```

#### 使用curl命令测试

**测试健康检查**
```bash
curl -X GET http://localhost:3000/health
```

**测试模型列表**
```bash
curl -X GET http://localhost:3000/api/models
```

**测试聊天接口（非流式）**
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "你好，请介绍一下你自己"}],
    "model": "deepseek",
    "stream": false,
    "options": {
      "deep_thinking": true
    }
  }'
```

**测试聊天接口（流式）**
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "请写一首关于春天的诗"}],
    "model": "deepseek",
    "stream": true
  }' \
  --no-buffer
```

**测试文本生成接口**
```bash
curl -X POST http://localhost:3000/api/text/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "请写一首关于春天的诗",
    "model": "deepseek",
    "task_type": "creative",
    "stream": false,
    "options": {
      "style": "现代诗",
      "format": "markdown"
    }
  }'
```

**测试OpenAI兼容接口**
```bash
# 获取模型列表（OpenAI格式）
curl -X GET http://localhost:3000/v1/models

# 聊天对话（OpenAI格式）
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek",
    "messages": [{"role": "user", "content": "Hello, how are you?"}],
    "stream": false,
    "temperature": 0.7,
    "max_tokens": 1000
  }'
```

**测试统计接口**
```bash
curl -X GET http://localhost:3000/stats
```

**测试模型重新加载**
```bash
curl -X POST http://localhost:3000/api/models/reload \
  -H "Content-Type: application/json"
```

## 部署建议

### 1. 生产环境

```bash
# 构建项目
npm run build

# 启动生产服务器
npm run server:prod
```

### 2. 进程管理

推荐使用 PM2 进行进程管理：

```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start dist/server/index.js --name "dangbei-api"

# 监控服务
pm2 monit
```

### 3. 反向代理

使用 Nginx 作为反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :3000
   
   # 使用其他端口
   npm run server -- --port 3001
   ```

2. **模型加载失败**
   - 检查 `models.json` 文件是否存在
   - 验证文件格式是否正确

3. **聊天请求超时**
   - 检查网络连接
   - 增加超时时间配置

## 支持和反馈

如有问题或建议，请通过以下方式联系：

- 项目地址：https://git.atjog.com/aier/dangbei-provider
- 问题反馈：创建 Issue
- 技术支持：查看项目文档
